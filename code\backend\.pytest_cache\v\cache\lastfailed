{"authentication/tests.py": true, "debug_test.py::test_registration": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_blocked_in_production": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_account_lockout_after_failed_attempts": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_user_cannot_login_with_invalid_credentials": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_test_accounts_force": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_account_lockout_after_failed_attempts": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_case_insensitive_email_login": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_invalid_email_login_failure": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_invalid_password_login_failure": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_provider_user_login": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_response_format_consistency": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_successful_login_resets_failed_attempts": true, "authentication/test_login_fix.py::LoginAuthenticationFixTests::test_valid_login_success": true, "authentication/tests/test_login_authentication.py::LoginAuthenticationViewTests::test_invalid_credentials_returns_400_status": true, "authentication/tests/test_login_authentication.py::LoginAuthenticationViewTests::test_nonexistent_user_returns_400_status": true, "authentication/tests/test_login_authentication.py::AccountLockoutTests::test_account_lockout_after_failed_attempts": true, "authentication/tests/test_login_authentication.py::AccountLockoutTests::test_locked_account_returns_423_status": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_all_accounts_database_consistency": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_email_not_verified_accounts": true, "test_consolidated_accounts.py::ConsolidatedTestAccountsTest::test_invalid_credentials_accounts": true, "test_consolidated_accounts.py::TestAccountPasswordValidationTest::test_password_strength_requirements": true, "test_consolidated_accounts.py::TestAccountAPIEndpointsTest::test_login_endpoint_with_all_accounts": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_database_settings_configuration": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_django_database_connection": true, "test_postgresql_config.py::PostgreSQLConnectionTest::test_postgresql_connection_available": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_authentication_tables_exist": true, "test_postgresql_config.py::PostgreSQLMigrationTest::test_catalog_tables_exist": true, "test_postgresql_config.py::PostgreSQLDataIntegrityTest::test_foreign_key_relationships": true, "test_postgresql_config.py::PostgreSQLPerformanceTest::test_database_indexes_exist": true, "test_postgresql_config.py::PostgreSQLEnvironmentTest::test_postgresql_version_compatibility": true, "test_audit_database_config.py::DatabaseConfigurationAuditTest::test_database_settings_structure_matches_reference": true, "test_audit_database_config.py::DatabaseConfigurationAuditTest::test_postgresql_configuration_matches_reference": true, "authentication/test_acceptance.py": true, "authentication/test_login_fix.py": true, "authentication/test_urls.py": true, "authentication/tests/test_login_authentication.py": true, "catalog/tests/test_account_management.py": true, "catalog/tests/test_provider_service_api_integration.py": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_environment_based_settings_structure_exists": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_environment_variable_configuration": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_postgresql_configuration_structure_when_available": true, "test_database_config_verification.py::DatabaseConfigurationVerificationTest::test_testing_database_configuration": true}