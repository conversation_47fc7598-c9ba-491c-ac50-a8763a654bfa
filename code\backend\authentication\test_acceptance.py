"""
Acceptance Tests for Authentication System
High-level behavior tests that define the expected user experience

These tests represent the acceptance criteria from the planning phase
and ensure 100% feature parity with the legacy system.
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core import mail
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import patch
import json

User = get_user_model()


class UserRegistrationAcceptanceTests(APITestCase):
    """
    Acceptance tests for user registration flow
    
    User Story: As a new user, I want to create an account quickly and securely 
    using my email, Google, or Apple account, so that I can access the application's features.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        self.register_url = '/api/auth/register/'
    
    def test_user_can_register_with_email_and_password(self):
        """
        Acceptance Criteria:
        - User provides email, password, first name, last name
        - Password must meet security requirements (12+ chars, mixed case, numbers, symbols)
        - Email must be unique and valid format
        - User receives verification email
        - User account is created but not verified initially
        """
        registration_data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'password': 'SecurePassword123!',
            'password_confirm': 'SecurePassword123!',
            'first_name': 'New',
            'last_name': 'User',
            'phone': '+**********'
        }
        
        response = self.client.post(self.register_url, registration_data, format='json')
        
        # Should create user successfully
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Should return JWT tokens
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        
        # User should exist in database
        user = User.objects.get(email='<EMAIL>')
        self.assertEqual(user.first_name, 'New')
        self.assertEqual(user.last_name, 'User')
        self.assertEqual(user.role, 'customer')  # Default role
        self.assertFalse(user.is_verified)  # Requires email verification
        
        # Should send verification email
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn('verify', mail.outbox[0].subject.lower())
    
    def test_user_cannot_register_with_weak_password(self):
        """
        Acceptance Criteria:
        - Password must be at least 12 characters
        - Must contain uppercase, lowercase, number, and special character
        - Common passwords should be rejected
        """
        weak_passwords = [
            'password',  # Too common
            '123456789',  # Too simple
            'Password',   # Missing number and symbol
            'Pass123',    # Too short
        ]
        
        for weak_password in weak_passwords:
            registration_data = {
                'email': f'test{weak_password}@example.com',
                'username': f'test{weak_password}',
                'password': weak_password,
                'password_confirm': weak_password,
                'first_name': 'Test',
                'last_name': 'User'
            }
            
            response = self.client.post(self.register_url, registration_data, format='json')
            
            # Should reject weak password
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn('password', response.data)
    
    def test_user_cannot_register_with_duplicate_email(self):
        """
        Acceptance Criteria:
        - Email addresses must be unique
        - Clear error message for duplicate email
        """
        # Create existing user
        User.objects.create_user(
            email='<EMAIL>',
            username='existing',
            password='ExistingPass123!'
        )
        
        # Try to register with same email
        registration_data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'password': 'NewPassword123!',
            'password_confirm': 'NewPassword123!',
            'first_name': 'New',
            'last_name': 'User'
        }
        
        response = self.client.post(self.register_url, registration_data, format='json')
        
        # Should reject duplicate email
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)


class UserLoginAcceptanceTests(APITestCase):
    """
    Acceptance tests for user login flow
    
    User Story: As a returning user, I want to log in securely and have my session 
    remembered, so I don't have to enter my credentials every time I open the app.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        self.login_url = '/api/auth/login/'
        
        # Create verified user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='loginuser',
            password='LoginPass123!',
            first_name='Login',
            last_name='User'
        )
        self.user.verify_email()
    
    def test_user_can_login_with_valid_credentials(self):
        """
        Acceptance Criteria:
        - User provides email and password
        - System validates credentials
        - Returns JWT access token (30 min) and refresh token (7 days)
        - Includes user profile information
        - Tracks last login activity
        """
        login_data = {
            'email': '<EMAIL>',
            'password': 'LoginPass123!'
        }
        
        response = self.client.post(self.login_url, login_data, format='json')
        
        # Should login successfully
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should return tokens and user data
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        
        # User data should include role and verification status
        user_data = response.data['user']
        self.assertEqual(user_data['email'], '<EMAIL>')
        self.assertEqual(user_data['role'], 'customer')
        self.assertTrue(user_data['is_verified'])
        
        # Should update last login
        self.user.refresh_from_db()
        self.assertIsNotNone(self.user.last_login)
    
    def test_user_cannot_login_with_invalid_credentials(self):
        """
        Acceptance Criteria:
        - Invalid email or password should be rejected
        - Generic error message for security
        - Failed attempts should be tracked
        """
        invalid_credentials = [
            {'email': '<EMAIL>', 'password': 'WrongPassword123!'},
            {'email': '<EMAIL>', 'password': 'LoginPass123!'},
            {'email': '<EMAIL>', 'password': ''},
        ]
        
        for credentials in invalid_credentials:
            response = self.client.post(self.login_url, credentials, format='json')

            # Should reject invalid credentials
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            # Check for either 'detail' (auth error) or field validation errors
            self.assertTrue('detail' in response.data or 'password' in response.data or 'email' in response.data)
    
    def test_account_lockout_after_failed_attempts(self):
        """
        Acceptance Criteria:
        - Account locked after 5 failed login attempts
        - Lockout duration of 30 minutes
        - Clear error message about lockout
        - Lockout should reset after successful login
        """
        login_data = {
            'email': '<EMAIL>',
            'password': 'WrongPassword123!'
        }
        
        # Make 4 failed attempts (should return 400)
        for i in range(4):
            response = self.client.post(self.login_url, login_data, format='json')
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # 5th attempt should trigger lockout (should return 423)
        response = self.client.post(self.login_url, login_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)
        
        # Even correct password should be locked
        correct_login_data = {
            'email': '<EMAIL>',
            'password': 'LoginPass123!'
        }
        response = self.client.post(self.login_url, correct_login_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)


class EmailVerificationAcceptanceTests(APITestCase):
    """
    Acceptance tests for email verification flow
    
    User Story: As a new user, I want to verify my email address to activate 
    my account and access all features.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        self.verify_url = '/api/auth/verify-email/'
        
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='verifyuser',
            password='VerifyPass123!'
        )
    
    def test_user_can_verify_email_with_valid_token(self):
        """
        Acceptance Criteria:
        - User receives verification email with unique token
        - Token is valid for 24 hours
        - Clicking link or entering token verifies account
        - Account status changes to active
        - User can access all features after verification
        """
        # This test will pass when EmailVerificationToken model is implemented
        # For now, we're defining the expected behavior
        
        # Simulate verification token (will be generated by system)
        verification_data = {
            'token': 'valid_verification_token_123'
        }
        
        response = self.client.post(self.verify_url, verification_data, format='json')
        
        # Should verify successfully (when implemented)
        # self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # User should be verified
        # self.user.refresh_from_db()
        # self.assertTrue(self.user.is_verified)
        # self.assertEqual(self.user.account_status, 'active')


class SocialAuthenticationAcceptanceTests(APITestCase):
    """
    Acceptance tests for social authentication (Google, Apple)
    
    User Story: As a user, I want to sign in with my Google or Apple account 
    for a quick and secure authentication experience.
    """
    
    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        self.social_auth_url = '/api/auth/social/'
    
    @patch('authentication.views.SocialAuthView._verify_google_token')
    def test_user_can_authenticate_with_google(self, mock_verify):
        """
        Acceptance Criteria:
        - User can sign in with Google account
        - System validates Google ID token
        - Creates new user if doesn't exist
        - Returns JWT tokens for authenticated session
        - User profile populated from Google data
        """
        # Mock Google token verification
        mock_verify.return_value = {
            'email': '<EMAIL>',
            'first_name': 'Google',
            'last_name': 'User',
            'user_id': 'google_123',
            'verified_email': True
        }
        
        social_auth_data = {
            'provider': 'google',
            'identity_token': 'valid_google_token',
            'email': '<EMAIL>',
            'first_name': 'Google',
            'last_name': 'User'
        }
        
        response = self.client.post(self.social_auth_url, social_auth_data, format='json')
        
        # Should authenticate successfully (when implemented)
        # self.assertEqual(response.status_code, status.HTTP_200_OK)
        # self.assertIn('access', response.data)
        # self.assertIn('refresh', response.data)
        
        # Should create user if doesn't exist
        # user = User.objects.get(email='<EMAIL>')
        # self.assertEqual(user.first_name, 'Google')
        # self.assertTrue(user.is_verified)  # Social accounts are pre-verified
