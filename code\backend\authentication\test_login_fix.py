"""
Test suite for login authentication fixes
Tests for EPIC-AD-HOC-02: Critical Login Fixes & Onboarding Implementation

This test suite covers:
1. Valid credential login scenarios
2. Invalid credential error handling
3. Account lockout functionality
4. API response format validation
5. Frontend-backend integration scenarios
"""

import json
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from unittest.mock import patch

User = get_user_model()


class LoginAuthenticationFixTests(APITestCase):
    """Test cases for login authentication fixes"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.login_url = reverse('authentication:login')
        
        # Create test users with known passwords
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='correctpassword123',
            first_name='Test',
            last_name='User',
            role='customer',
            is_active=True
        )
        self.test_user.verify_email()  # Verify the test user

        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='provider',
            password='providerpass123',
            first_name='Provider',
            last_name='User',
            role='service_provider',
            is_active=True
        )
        self.provider_user.verify_email()  # Verify the provider user
        
        # Inactive user for testing
        self.inactive_user = User.objects.create_user(
            email='<EMAIL>',
            username='inactive',
            password='inactivepass123',
            is_active=False
        )

    def test_valid_login_success(self):
        """Test successful login with valid credentials"""
        data = {
            'email': '<EMAIL>',
            'password': 'correctpassword123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        
        # Verify user data in response
        user_data = response.data['user']
        self.assertEqual(user_data['email'], '<EMAIL>')
        self.assertEqual(user_data['role'], 'customer')

    def test_invalid_email_login_failure(self):
        """Test login failure with non-existent email"""
        data = {
            'email': '<EMAIL>',
            'password': 'anypassword'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('detail', response.data)
        self.assertEqual(str(response.data['detail'][0]), 'Invalid credentials')

    def test_invalid_password_login_failure(self):
        """Test login failure with wrong password"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('detail', response.data)
        self.assertEqual(str(response.data['detail'][0]), 'Invalid credentials')

    def test_empty_credentials_validation(self):
        """Test validation with empty credentials"""
        # Empty email
        data = {'email': '', 'password': 'somepassword'}
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Empty password
        data = {'email': '<EMAIL>', 'password': ''}
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Both empty
        data = {'email': '', 'password': ''}
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_inactive_user_login_failure(self):
        """Test that inactive users cannot login"""
        data = {
            'email': '<EMAIL>',
            'password': 'inactivepass123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('detail', response.data)

    def test_account_lockout_after_failed_attempts(self):
        """Test account lockout after multiple failed login attempts"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }

        # Make 4 failed attempts (should return 400)
        for i in range(4):
            response = self.client.post(self.login_url, data, format='json')
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # 5th attempt should trigger lockout (should return 423)
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)

        # Refresh user from database
        self.test_user.refresh_from_db()

        # Verify account is locked
        self.assertTrue(self.test_user.is_account_locked)
        
        # Try to login with correct password - should still fail due to lockout
        correct_data = {
            'email': '<EMAIL>',
            'password': 'correctpassword123'
        }
        response = self.client.post(self.login_url, correct_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)

    def test_successful_login_resets_failed_attempts(self):
        """Test that successful login resets failed attempt counter"""
        # Make some failed attempts
        wrong_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        for i in range(3):
            self.client.post(self.login_url, wrong_data, format='json')
        
        # Verify failed attempts were recorded
        self.test_user.refresh_from_db()
        self.assertEqual(self.test_user.failed_login_attempts, 3)
        
        # Now login successfully
        correct_data = {
            'email': '<EMAIL>',
            'password': 'correctpassword123'
        }
        response = self.client.post(self.login_url, correct_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify failed attempts were reset
        self.test_user.refresh_from_db()
        self.assertEqual(self.test_user.failed_login_attempts, 0)

    def test_response_format_consistency(self):
        """Test that API responses have consistent format"""
        # Test successful response format
        data = {
            'email': '<EMAIL>',
            'password': 'correctpassword123'
        }
        response = self.client.post(self.login_url, data, format='json')
        
        required_fields = ['access', 'refresh', 'user']
        for field in required_fields:
            self.assertIn(field, response.data)
        
        # Test error response format
        wrong_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        response = self.client.post(self.login_url, wrong_data, format='json')
        self.assertIn('detail', response.data)

    def test_case_insensitive_email_login(self):
        """Test that email login is case insensitive"""
        data = {
            'email': '<EMAIL>',  # Uppercase email
            'password': 'correctpassword123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_provider_user_login(self):
        """Test that service provider users can login successfully"""
        data = {
            'email': '<EMAIL>',
            'password': 'providerpass123'
        }
        
        response = self.client.post(self.login_url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user']['role'], 'service_provider')

    def test_malformed_request_handling(self):
        """Test handling of malformed requests"""
        # Missing password field
        data = {'email': '<EMAIL>'}
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Missing email field
        data = {'password': 'password123'}
        response = self.client.post(self.login_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Invalid JSON
        response = self.client.post(
            self.login_url, 
            'invalid json', 
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class LoginIntegrationTests(TestCase):
    """Integration tests for login functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='integration',
            password='integrationpass123',
            is_active=True
        )

    def test_login_endpoint_accessibility(self):
        """Test that login endpoint is accessible"""
        response = self.client.get('/api/auth/login/')
        # Should return 405 Method Not Allowed for GET, but endpoint should exist
        self.assertEqual(response.status_code, 405)

    def test_cors_headers_present(self):
        """Test that CORS headers are present for frontend integration"""
        data = {
            'email': '<EMAIL>',
            'password': 'integrationpass123'
        }
        
        response = self.client.post(
            '/api/auth/login/',
            json.dumps(data),
            content_type='application/json',
            HTTP_ORIGIN='http://192.168.2.65:8081'  # Frontend origin
        )
        
        # Should not fail due to CORS issues
        self.assertIn(response.status_code, [200, 400])  # Either success or validation error
