"""
Django management command to cleanup test accounts and related data
Safely removes test accounts and their associated data from the database
"""
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.db import transaction
from django.conf import settings

from catalog.models import ServiceProvider, Service

User = get_user_model()


class Command(BaseCommand):
    help = 'Cleanup test accounts and related data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force deletion without confirmation prompt',
        )
        parser.add_argument(
            '--customers-only',
            action='store_true',
            help='Delete only customer test accounts',
        )
        parser.add_argument(
            '--providers-only',
            action='store_true',
            help='Delete only provider test accounts',
        )

    def handle(self, *args, **options):
        # Security check - only allow in development/testing
        if not self.is_development_environment():
            raise CommandError(
                'Test account cleanup is only allowed in development/testing environments. '
                'Current environment: {}'.format(getattr(settings, 'ENVIRONMENT', 'unknown'))
            )

        self.dry_run = options['dry_run']
        self.force = options['force']
        self.customers_only = options['customers_only']
        self.providers_only = options['providers_only']

        self.stdout.write(
            self.style.WARNING('🧹 Cleaning up test accounts...')
        )

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('🔍 DRY RUN MODE - No data will be deleted')
            )

        try:
            # Get test accounts to delete
            accounts_to_delete = self.get_test_accounts()
            
            if not accounts_to_delete['customers'] and not accounts_to_delete['providers'] and not accounts_to_delete['admins']:
                self.stdout.write(
                    self.style.SUCCESS('✅ No test accounts found to delete')
                )
                return

            # Show what will be deleted
            self.show_deletion_summary(accounts_to_delete)

            # Confirm deletion unless forced or dry run
            if not self.dry_run and not self.force:
                if not self.confirm_deletion():
                    self.stdout.write(
                        self.style.WARNING('❌ Cleanup cancelled by user')
                    )
                    return

            # Perform deletion
            if not self.dry_run:
                with transaction.atomic():
                    deleted_counts = self.delete_test_accounts(accounts_to_delete)
                    self.print_deletion_results(deleted_counts)
            else:
                self.stdout.write(
                    self.style.SUCCESS('✅ Dry run completed - no data was deleted')
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to cleanup test accounts: {e}')
            )
            raise CommandError(f'Test account cleanup failed: {e}')

    def is_development_environment(self):
        """Check if we're in a development environment"""
        environment = getattr(settings, 'ENVIRONMENT', 'development')
        debug_mode = getattr(settings, 'DEBUG', False)
        
        # Allow in development, testing, or when DEBUG is True
        return environment in ['development', 'testing'] or debug_mode

    def get_test_accounts(self):
        """Get all test accounts to be deleted"""
        accounts = {
            'customers': [],
            'providers': [],
            'admins': [],
            'services': []
        }

        # Get test users
        test_users = User.objects.filter(is_test_account=True)

        if not self.providers_only:
            # Get customer test accounts
            customer_users = test_users.filter(role='customer')
            accounts['customers'] = list(customer_users)

        if not self.customers_only:
            # Get provider test accounts
            provider_users = test_users.filter(role='service_provider')
            accounts['providers'] = list(provider_users)

            # Get associated services
            provider_profiles = ServiceProvider.objects.filter(
                user__in=provider_users
            )
            services = Service.objects.filter(provider__in=provider_profiles)
            accounts['services'] = list(services)

        # Include admin accounts only when force flag is used (for safety)
        if self.force and not self.customers_only and not self.providers_only:
            admin_users = test_users.filter(role='admin')
            accounts['admins'] = list(admin_users)

        return accounts

    def show_deletion_summary(self, accounts):
        """Show what will be deleted"""
        self.stdout.write('\n📋 Accounts to be deleted:')
        self.stdout.write('-' * 40)

        if accounts['customers']:
            self.stdout.write(f'\n👤 Customer Accounts ({len(accounts["customers"])}):')
            for customer in accounts['customers']:
                self.stdout.write(f'   📧 {customer.email} - {customer.get_full_name()}')

        if accounts['providers']:
            self.stdout.write(f'\n🏢 Provider Accounts ({len(accounts["providers"])}):')
            for provider_user in accounts['providers']:
                try:
                    provider_profile = provider_user.provider_profile
                    business_name = provider_profile.business_name
                except:
                    business_name = 'No business profile'
                self.stdout.write(f'   📧 {provider_user.email} - {business_name}')

        if accounts['admins']:
            self.stdout.write(f'\n👑 Admin Accounts ({len(accounts["admins"])}):')
            for admin in accounts['admins']:
                self.stdout.write(f'   📧 {admin.email} - {admin.get_full_name()}')

        if accounts['services']:
            self.stdout.write(f'\n🛍️  Associated Services ({len(accounts["services"])}):')
            for service in accounts['services'][:5]:  # Show first 5
                self.stdout.write(f'   🔹 {service.name} (${service.base_price})')
            if len(accounts['services']) > 5:
                self.stdout.write(f'   ... and {len(accounts["services"]) - 5} more services')

        # Show totals
        total_users = len(accounts['customers']) + len(accounts['providers']) + len(accounts['admins'])
        self.stdout.write(f'\n📊 Total to delete:')
        self.stdout.write(f'   👥 Users: {total_users}')
        self.stdout.write(f'   🛍️  Services: {len(accounts["services"])}')

    def confirm_deletion(self):
        """Ask user to confirm deletion"""
        self.stdout.write(
            self.style.WARNING('\n⚠️  This will permanently delete the above test accounts and all related data!')
        )
        
        response = input('\nAre you sure you want to continue? (yes/no): ')
        return response.lower() in ['yes', 'y']

    def delete_test_accounts(self, accounts):
        """Delete the test accounts and related data"""
        deleted_counts = {
            'customers': 0,
            'providers': 0,
            'admins': 0,
            'services': 0
        }

        # Delete services first (to avoid foreign key constraints)
        if accounts['services']:
            self.stdout.write('\n🗑️  Deleting services...')
            for service in accounts['services']:
                service.delete()
                deleted_counts['services'] += 1
                self.stdout.write(f'   ✅ Deleted service: {service.name}')

        # Delete provider accounts
        if accounts['providers']:
            self.stdout.write('\n🗑️  Deleting provider accounts...')
            for provider_user in accounts['providers']:
                email = provider_user.email
                provider_user.delete()  # This will cascade to ServiceProvider
                deleted_counts['providers'] += 1
                self.stdout.write(f'   ✅ Deleted provider: {email}')

        # Delete customer accounts
        if accounts['customers']:
            self.stdout.write('\n🗑️  Deleting customer accounts...')
            for customer in accounts['customers']:
                email = customer.email
                customer.delete()
                deleted_counts['customers'] += 1
                self.stdout.write(f'   ✅ Deleted customer: {email}')

        # Delete admin accounts (only when force flag is used)
        if accounts['admins']:
            self.stdout.write('\n🗑️  Deleting admin accounts...')
            for admin in accounts['admins']:
                email = admin.email
                admin.delete()
                deleted_counts['admins'] += 1
                self.stdout.write(f'   ✅ Deleted admin: {email}')

        return deleted_counts

    def print_deletion_results(self, deleted_counts):
        """Print deletion results"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS('🎉 TEST ACCOUNTS CLEANUP COMPLETED!')
        )
        self.stdout.write('='*50)

        self.stdout.write(f'\n📊 Deletion Summary:')
        self.stdout.write(f'   👤 Customers deleted: {deleted_counts["customers"]}')
        self.stdout.write(f'   🏢 Providers deleted: {deleted_counts["providers"]}')
        if deleted_counts["admins"] > 0:
            self.stdout.write(f'   👑 Admins deleted: {deleted_counts["admins"]}')
        self.stdout.write(f'   🛍️  Services deleted: {deleted_counts["services"]}')

        total_users = deleted_counts['customers'] + deleted_counts['providers'] + deleted_counts['admins']
        self.stdout.write(f'   👥 Total users deleted: {total_users}')

        self.stdout.write(f'\n💡 Tips:')
        self.stdout.write(f'   • Use "python manage.py create_test_accounts" to recreate accounts')
        self.stdout.write(f'   • Use --dry-run to preview deletions without executing')
        
        self.stdout.write('\n✨ Database cleaned up successfully!')
