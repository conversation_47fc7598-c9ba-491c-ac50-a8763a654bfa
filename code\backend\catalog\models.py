"""
Service Catalog models for Vierla Beauty Services Marketplace
Enhanced Django implementation with mobile-first design
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator, RegexValidator
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from decimal import Decimal
import uuid

User = get_user_model()


class ServiceManager(models.Manager):
    """Custom manager for Service model with business logic"""

    def active(self):
        """Get only active services"""
        return self.filter(is_active=True)

    def available(self):
        """Get only available services"""
        return self.filter(is_active=True, is_available=True)

    def for_provider(self, provider):
        """Get services for a specific provider"""
        return self.filter(provider=provider)

    def popular(self):
        """Get popular services"""
        return self.filter(is_popular=True, is_active=True, is_available=True)

    def by_category(self, category):
        """Get services by category"""
        return self.filter(category=category, is_active=True)

    def in_price_range(self, min_price=None, max_price=None):
        """Get services within price range"""
        queryset = self.active()
        if min_price is not None:
            queryset = queryset.filter(base_price__gte=min_price)
        if max_price is not None:
            queryset = queryset.filter(base_price__lte=max_price)
        return queryset

    def by_duration_range(self, min_duration=None, max_duration=None):
        """Get services within duration range"""
        queryset = self.active()
        if min_duration is not None:
            queryset = queryset.filter(duration__gte=min_duration)
        if max_duration is not None:
            queryset = queryset.filter(duration__lte=max_duration)
        return queryset

    def create_service(self, provider, **kwargs):
        """Create a new service with business logic validation"""
        # Check service limit for unverified providers
        if not provider.is_verified:
            active_count = self.for_provider(provider).active().count()
            if active_count >= 3:
                raise ValidationError(
                    "Unverified providers are limited to 3 active services. "
                    "Please verify your account to add more services."
                )

        # Check for duplicate names
        name = kwargs.get('name', '').strip()
        if self.for_provider(provider).active().filter(name__iexact=name).exists():
            raise ValidationError(f"Service with name '{name}' already exists for this provider")

        # Create the service
        kwargs['provider'] = provider
        return self.create(**kwargs)


class ServiceCategory(models.Model):
    """
    Service categories for organizing beauty services
    Enhanced with mobile-first design and hierarchical support
    """

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Basic Information
    name = models.CharField(
        _('category name'),
        max_length=100,
        unique=True,
        help_text=_('Name of the service category')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=120,
        unique=True,
        help_text=_('URL-friendly version of the name')
    )
    description = models.TextField(
        _('description'),
        help_text=_('Detailed description of the category')
    )

    # Visual Elements
    icon = models.CharField(
        _('icon'),
        max_length=50,
        help_text=_('Icon identifier (emoji or icon name)')
    )
    color = models.CharField(
        _('color'),
        max_length=7,
        default='#8FBC8F',  # Sage green default
        validators=[RegexValidator(
            regex=r'^#[0-9A-Fa-f]{6}$',
            message=_('Color must be a valid hex color code')
        )],
        help_text=_('Hex color code for the category')
    )
    image = models.ImageField(
        _('category image'),
        upload_to='categories/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Category banner image')
    )

    # Hierarchical Support
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='subcategories',
        help_text=_('Parent category for hierarchical organization')
    )

    # Status and Ordering
    is_popular = models.BooleanField(
        _('is popular'),
        default=False,
        help_text=_('Mark as popular category for featured display')
    )
    is_active = models.BooleanField(
        _('is active'),
        default=True,
        help_text=_('Whether this category is active and visible')
    )
    sort_order = models.PositiveIntegerField(
        _('sort order'),
        default=0,
        help_text=_('Order for displaying categories')
    )

    # Mobile Optimization
    mobile_icon = models.CharField(
        _('mobile icon'),
        max_length=50,
        blank=True,
        help_text=_('Mobile-specific icon identifier')
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Service Category')
        verbose_name_plural = _('Service Categories')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['slug']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_popular']),
            models.Index(fields=['sort_order']),
        ]

    def __str__(self):
        return self.name

    @property
    def has_subcategories(self):
        """Check if this category has subcategories"""
        return self.subcategories.exists()

    @property
    def service_count(self):
        """Get the number of active services in this category"""
        return self.services.filter(is_active=True).count()


class ServiceProvider(models.Model):
    """
    Service providers offering beauty services
    Enhanced with comprehensive business information and mobile optimization
    """

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # User Relationship
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='service_provider',
        help_text=_('User account associated with this provider')
    )

    # Business Information
    business_name = models.CharField(
        _('business name'),
        max_length=200,
        help_text=_('Name of the business or service provider')
    )
    business_description = models.TextField(
        _('business description'),
        help_text=_('Detailed description of the business and services offered')
    )
    business_phone = models.CharField(
        _('business phone'),
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Phone number must be in valid format')
        )],
        help_text=_('Business contact phone number')
    )
    business_email = models.EmailField(
        _('business email'),
        help_text=_('Business contact email address')
    )

    # Location Information
    address = models.TextField(
        _('business address'),
        help_text=_('Full business address')
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        help_text=_('Business city')
    )
    state = models.CharField(
        _('state/province'),
        max_length=100,
        help_text=_('State or province')
    )
    zip_code = models.CharField(
        _('zip/postal code'),
        max_length=20,
        help_text=_('Zip or postal code')
    )
    country = models.CharField(
        _('country'),
        max_length=100,
        default='Canada',
        help_text=_('Country')
    )

    # Geolocation for mobile app
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=8,
        blank=True,
        null=True,
        help_text=_('Latitude coordinate for location-based services')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=11,
        decimal_places=8,
        blank=True,
        null=True,
        help_text=_('Longitude coordinate for location-based services')
    )

    # Online Presence
    website = models.URLField(
        _('website'),
        blank=True,
        help_text=_('Business website URL')
    )
    instagram_handle = models.CharField(
        _('Instagram handle'),
        max_length=100,
        blank=True,
        help_text=_('Instagram username without @')
    )
    facebook_url = models.URLField(
        _('Facebook page'),
        blank=True,
        help_text=_('Facebook business page URL')
    )

    # Media
    profile_image = models.ImageField(
        _('profile image'),
        upload_to='providers/profiles/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Business profile picture')
    )
    cover_image = models.ImageField(
        _('cover image'),
        upload_to='providers/covers/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Business cover/banner image')
    )

    # Business Status and Verification
    is_verified = models.BooleanField(
        _('is verified'),
        default=False,
        help_text=_('Whether the business is verified by admin')
    )
    is_featured = models.BooleanField(
        _('is featured'),
        default=False,
        help_text=_('Whether to feature this provider prominently')
    )
    is_active = models.BooleanField(
        _('is active'),
        default=True,
        help_text=_('Whether the provider is active and accepting bookings')
    )

    # Categories
    categories = models.ManyToManyField(
        ServiceCategory,
        related_name='providers',
        help_text=_('Service categories offered by this provider')
    )

    # Ratings and Reviews
    rating = models.DecimalField(
        _('average rating'),
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[
            MinValueValidator(Decimal('0.00')),
            MaxValueValidator(Decimal('5.00'))
        ],
        help_text=_('Average rating from customer reviews')
    )
    review_count = models.PositiveIntegerField(
        _('review count'),
        default=0,
        help_text=_('Total number of reviews received')
    )

    # Business Metrics
    total_bookings = models.PositiveIntegerField(
        _('total bookings'),
        default=0,
        help_text=_('Total number of completed bookings')
    )
    years_of_experience = models.PositiveIntegerField(
        _('years of experience'),
        blank=True,
        null=True,
        help_text=_('Years of professional experience')
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Service Provider')
        verbose_name_plural = _('Service Providers')
        ordering = ['-is_featured', '-rating', 'business_name']
        indexes = [
            models.Index(fields=['business_name']),
            models.Index(fields=['city']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['rating']),
            models.Index(fields=['is_featured']),
        ]

    def __str__(self):
        return self.business_name

    @property
    def full_address(self):
        """Get formatted full address"""
        return f"{self.address}, {self.city}, {self.state} {self.zip_code}, {self.country}"

    @property
    def has_location(self):
        """Check if provider has geolocation coordinates"""
        return self.latitude is not None and self.longitude is not None

    @property
    def active_services_count(self):
        """Get count of active services"""
        return self.services.filter(is_active=True).count()

    @property
    def available_services_count(self):
        """Get count of available services"""
        return self.services.filter(is_active=True, is_available=True).count()

    @property
    def service_limit(self):
        """Get service limit based on verification status"""
        return None if self.is_verified else 3

    @property
    def can_add_service(self):
        """Check if provider can add more services"""
        if self.is_verified:
            return True
        return self.active_services_count < 3

    @property
    def services_remaining(self):
        """Get number of services remaining for unverified providers"""
        if self.is_verified:
            return None
        return max(0, 3 - self.active_services_count)

    def get_services_by_category(self, category):
        """Get services filtered by category"""
        return self.services.filter(category=category, is_active=True)

    def get_popular_services(self):
        """Get popular services for this provider"""
        return self.services.filter(is_popular=True, is_active=True)

    def update_rating(self):
        """Update average rating based on reviews (placeholder for future implementation)"""
        # This would be implemented when review system is added
        pass


class Service(models.Model):
    """
    Individual services offered by providers
    Enhanced with mobile-first design and comprehensive service information
    """

    PRICE_TYPE_CHOICES = [
        ('fixed', _('Fixed Price')),
        ('hourly', _('Hourly Rate')),
        ('range', _('Price Range')),
        ('consultation', _('Consultation Required')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Provider Relationship
    provider = models.ForeignKey(
        ServiceProvider,
        on_delete=models.CASCADE,
        related_name='services',
        help_text=_('Service provider offering this service')
    )

    # Category Relationship
    category = models.ForeignKey(
        ServiceCategory,
        on_delete=models.CASCADE,
        related_name='services',
        help_text=_('Category this service belongs to')
    )

    # Service Information
    name = models.CharField(
        _('service name'),
        max_length=200,
        help_text=_('Name of the service')
    )
    description = models.TextField(
        _('service description'),
        help_text=_('Detailed description of the service')
    )
    short_description = models.CharField(
        _('short description'),
        max_length=255,
        blank=True,
        help_text=_('Brief description for mobile display')
    )
    mobile_description = models.TextField(
        _('mobile description'),
        blank=True,
        help_text=_('Mobile-optimized description')
    )

    # Pricing Information
    base_price = models.DecimalField(
        _('base price'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Base price for the service')
    )
    price_type = models.CharField(
        _('price type'),
        max_length=20,
        choices=PRICE_TYPE_CHOICES,
        default='fixed',
        help_text=_('Type of pricing structure')
    )
    max_price = models.DecimalField(
        _('maximum price'),
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Maximum price for range pricing')
    )

    # Duration Information
    duration = models.PositiveIntegerField(
        _('duration (minutes)'),
        help_text=_('Service duration in minutes')
    )
    buffer_time = models.PositiveIntegerField(
        _('buffer time (minutes)'),
        default=15,
        help_text=_('Buffer time between appointments')
    )

    # Service Media
    image = models.ImageField(
        _('service image'),
        upload_to='services/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Main service image')
    )

    # Service Status
    is_popular = models.BooleanField(
        _('is popular'),
        default=False,
        help_text=_('Mark as popular service for featured display')
    )
    is_available = models.BooleanField(
        _('is available'),
        default=True,
        help_text=_('Whether the service is currently available for booking')
    )
    is_active = models.BooleanField(
        _('is active'),
        default=True,
        help_text=_('Whether the service is active and visible')
    )

    # Additional Information
    requirements = models.TextField(
        _('requirements'),
        blank=True,
        help_text=_('Special requirements or prerequisites for the service')
    )
    preparation_instructions = models.TextField(
        _('preparation instructions'),
        blank=True,
        help_text=_('Instructions for client preparation before the service')
    )

    # Metrics
    booking_count = models.PositiveIntegerField(
        _('booking count'),
        default=0,
        help_text=_('Total number of bookings for this service')
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Custom manager
    objects = ServiceManager()

    def __str__(self):
        return f"{self.name} - {self.provider.business_name}"

    @property
    def display_price(self):
        """Get formatted price display"""
        if self.price_type == 'range' and self.max_price:
            return f"${self.base_price} - ${self.max_price}"
        elif self.price_type == 'hourly':
            return f"${self.base_price}/hour"
        elif self.price_type == 'consultation':
            return "Consultation Required"
        else:
            return f"${self.base_price}"

    @property
    def display_duration(self):
        """Get formatted duration display"""
        hours = self.duration // 60
        minutes = self.duration % 60

        if hours > 0 and minutes > 0:
            return f"{hours}h {minutes}m"
        elif hours > 0:
            return f"{hours}h"
        else:
            return f"{minutes}m"

    @property
    def total_duration_with_buffer(self):
        """Get total duration including buffer time"""
        return self.duration + self.buffer_time

    @property
    def is_bookable(self):
        """Check if service is available for booking"""
        return self.is_active and self.is_available and self.provider.is_active

    @property
    def price_range_display(self):
        """Get price range for display"""
        if self.price_type == 'range' and self.max_price:
            return f"${self.base_price:.2f} - ${self.max_price:.2f}"
        return f"${self.base_price:.2f}"

    def toggle_availability(self):
        """Toggle service availability"""
        self.is_available = not self.is_available
        self.save(update_fields=['is_available', 'updated_at'])
        return self.is_available

    def soft_delete(self):
        """Soft delete the service by setting is_active to False"""
        self.is_active = False
        self.is_available = False
        self.save(update_fields=['is_active', 'is_available', 'updated_at'])

    def restore(self):
        """Restore a soft-deleted service"""
        self.is_active = True
        self.save(update_fields=['is_active', 'updated_at'])

    def increment_booking_count(self):
        """Increment booking count when a booking is made"""
        self.booking_count += 1
        self.save(update_fields=['booking_count', 'updated_at'])

    def can_be_edited_by(self, user):
        """Check if user can edit this service"""
        return (
            user.is_authenticated and
            user.role == 'service_provider' and
            hasattr(user, 'provider_profile') and
            self.provider.user == user
        )

    def get_similar_services(self, limit=5):
        """Get similar services from the same category"""
        return Service.objects.filter(
            category=self.category,
            is_active=True,
            is_available=True
        ).exclude(id=self.id)[:limit]

    def clean(self):
        """Model validation"""
        super().clean()

        # Validate price range
        if self.price_type == 'range':
            if not self.max_price:
                raise ValidationError("max_price is required when price_type is 'range'")
            if self.max_price <= self.base_price:
                raise ValidationError("max_price must be greater than base_price")

        # Validate duration
        if self.duration <= 0:
            raise ValidationError("Duration must be greater than 0")

        # Validate base price
        if self.base_price <= 0:
            raise ValidationError("Base price must be greater than 0")

    def save(self, *args, **kwargs):
        """Override save to add business logic"""
        # Run model validation
        self.full_clean()

        # Auto-generate short description if not provided
        if not self.short_description and self.description:
            self.short_description = self.description[:255]

        # Auto-generate mobile description if not provided
        if not self.mobile_description and self.description:
            # Create a mobile-friendly version (shorter)
            self.mobile_description = self.description[:200] + "..." if len(self.description) > 200 else self.description

        super().save(*args, **kwargs)

    def activate(self):
        """Activate the service"""
        if not self.is_active:
            self.is_active = True
            self.save(update_fields=['is_active', 'updated_at'])
        return self

    def deactivate(self):
        """Deactivate the service"""
        if self.is_active:
            self.is_active = False
            self.is_available = False  # Also make unavailable
            self.save(update_fields=['is_active', 'is_available', 'updated_at'])
        return self

    def make_available(self):
        """Make service available for booking"""
        if self.is_active and not self.is_available:
            self.is_available = True
            self.save(update_fields=['is_available', 'updated_at'])
        return self

    def make_unavailable(self):
        """Make service unavailable for booking"""
        if self.is_available:
            self.is_available = False
            self.save(update_fields=['is_available', 'updated_at'])
        return self

    def mark_popular(self):
        """Mark service as popular"""
        if not self.is_popular:
            self.is_popular = True
            self.save(update_fields=['is_popular', 'updated_at'])
        return self

    def unmark_popular(self):
        """Remove popular status"""
        if self.is_popular:
            self.is_popular = False
            self.save(update_fields=['is_popular', 'updated_at'])
        return self

    def update_price(self, new_price, price_type='fixed', max_price=None):
        """Update service pricing with validation"""
        if new_price <= 0:
            raise ValidationError("Price must be greater than 0")

        if price_type == 'range' and (not max_price or max_price <= new_price):
            raise ValidationError("max_price must be provided and greater than base_price for range pricing")

        self.base_price = new_price
        self.price_type = price_type
        self.max_price = max_price
        self.save(update_fields=['base_price', 'price_type', 'max_price', 'updated_at'])
        return self

    class Meta:
        verbose_name = _('Service')
        verbose_name_plural = _('Services')
        ordering = ['-is_popular', 'base_price', 'name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['base_price']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_available']),
            models.Index(fields=['is_popular']),
            models.Index(fields=['created_at']),
            models.Index(fields=['provider', 'is_active']),
            models.Index(fields=['category', 'is_active']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['provider', 'name'],
                condition=models.Q(is_active=True),
                name='unique_active_service_name_per_provider'
            ),
            models.CheckConstraint(
                condition=models.Q(base_price__gte=0),
                name='positive_base_price'
            ),
            models.CheckConstraint(
                condition=models.Q(duration__gt=0),
                name='positive_duration'
            ),
        ]
