#!/usr/bin/env python3
"""
PostgreSQL Setup Script for Vierla Development Environment
This script sets up PostgreSQL database and user for development.
"""

import os
import sys
import subprocess
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def run_command(command, check=True):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip()
    except subprocess.CalledProcessError as e:
        return None, e.stderr

def check_postgresql_service():
    """Check if PostgreSQL service is running."""
    stdout, stderr = run_command('pg_isready -h localhost -p 5432', check=False)
    return stdout and 'accepting connections' in stdout

def create_database_and_user():
    """Create the Vierla database and user."""
    try:
        # Try to connect as postgres user (assuming no password for local development)
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            user='postgres',
            database='postgres'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Create user if not exists
        cursor.execute("""
            DO $$ 
            BEGIN
                IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'vierla_user') THEN
                    CREATE USER vierla_user WITH PASSWORD 'vierla_password';
                END IF;
            END
            $$;
        """)
        
        # Create database if not exists
        cursor.execute("""
            SELECT 1 FROM pg_database WHERE datname = 'vierla_db'
        """)
        if not cursor.fetchone():
            cursor.execute("CREATE DATABASE vierla_db OWNER vierla_user")
        
        # Grant privileges
        cursor.execute("GRANT ALL PRIVILEGES ON DATABASE vierla_db TO vierla_user")
        
        cursor.close()
        conn.close()
        
        print("✅ PostgreSQL database and user created successfully!")
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL setup failed: {e}")
        return False

def test_connection():
    """Test the database connection with the configured credentials."""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port='5432',
            user='vierla_user',
            password='vierla_password',
            database='vierla_db'
        )
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        
        print(f"✅ Database connection successful!")
        print(f"   PostgreSQL version: {version}")
        return True
        
    except psycopg2.Error as e:
        print(f"❌ Database connection failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🔧 Setting up PostgreSQL for Vierla development...")
    
    # Check if PostgreSQL is running
    if not check_postgresql_service():
        print("❌ PostgreSQL service is not running.")
        print("   Please start PostgreSQL service and try again.")
        print("   On Windows: net start postgresql-x64-17")
        print("   On macOS: brew services start postgresql")
        print("   On Linux: sudo systemctl start postgresql")
        return False
    
    print("✅ PostgreSQL service is running")
    
    # Create database and user
    if not create_database_and_user():
        print("❌ Failed to create database and user")
        print("   Falling back to SQLite for development...")
        return False
    
    # Test connection
    if not test_connection():
        print("❌ Failed to connect to database")
        return False
    
    print("\n🎉 PostgreSQL setup completed successfully!")
    print("   Database: vierla_db")
    print("   User: vierla_user")
    print("   Password: vierla_password")
    print("   Host: localhost")
    print("   Port: 5432")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
