"""
AUDIT TEST: Database Configuration Inconsistency
This test proves that the current database configuration doesn't match
the reference architecture and PostgreSQL migration is incomplete.
"""

import pytest
from django.conf import settings
from django.test import TestCase
from django.db import connection


class DatabaseConfigurationAuditTest(TestCase):
    """
    AUDIT FINDING: Database configuration inconsistency
    EXPECTED RESULT: This test should FAIL, proving the issue exists
    """
    
    def test_postgresql_configuration_matches_reference(self):
        """
        AUDIT TEST: Verify PostgreSQL is properly configured as per reference architecture
        EXPECTED: FAIL - Current system still uses SQLite despite PostgreSQL configuration
        """
        # Reference architecture expects PostgreSQL
        db_config = settings.DATABASES['default']
        
        # This should pass but will fail due to SQLite fallback
        self.assertEqual(
            db_config['ENGINE'], 
            'django.db.backends.postgresql',
            "Database should use PostgreSQL as per reference architecture"
        )
        
        # Verify actual connection is PostgreSQL
        with connection.cursor() as cursor:
            # This will fail on SQLite as it doesn't have version() function
            cursor.execute("SELECT version();")
            result = cursor.fetchone()
            self.assertIn('PostgreSQL', result[0])
    
    def test_database_settings_structure_matches_reference(self):
        """
        AUDIT TEST: Verify database settings structure matches reference-code
        EXPECTED: FAIL - Current settings structure differs from reference
        """
        db_config = settings.DATABASES['default']
        
        # Reference architecture has these optimization settings
        expected_settings = {
            'CONN_MAX_AGE': 600,
            'CONN_HEALTH_CHECKS': True,
            'OPTIONS': {
                'sslmode': 'prefer',
                'connect_timeout': 10,
            }
        }
        
        # These should exist but don't in current implementation
        self.assertIn('CONN_MAX_AGE', db_config)
        self.assertIn('CONN_HEALTH_CHECKS', db_config)
        self.assertIn('OPTIONS', db_config)
        
        if 'OPTIONS' in db_config:
            self.assertIn('sslmode', db_config['OPTIONS'])
            self.assertIn('connect_timeout', db_config['OPTIONS'])


class APIEndpointStructureAuditTest(TestCase):
    """
    AUDIT FINDING: API endpoint structure doesn't match reference architecture
    EXPECTED RESULT: This test should FAIL, proving the inconsistency
    """
    
    def test_api_versioning_structure_matches_reference(self):
        """
        AUDIT TEST: Verify API versioning follows reference architecture
        EXPECTED: FAIL - Current API lacks proper v1 versioning structure
        """
        from django.urls import reverse, NoReverseMatch
        
        # Reference architecture has versioned API endpoints
        expected_v1_endpoints = [
            'api:v1:auth:login',
            'api:v1:auth:register', 
            'api:v1:providers:list',
            'api:v1:services:list',
            'api:v1:categories:list',
        ]
        
        for endpoint in expected_v1_endpoints:
            with self.assertRaises(NoReverseMatch, 
                                 msg=f"Endpoint {endpoint} should exist but doesn't"):
                reverse(endpoint)
    
    def test_settings_module_structure_matches_reference(self):
        """
        AUDIT TEST: Verify settings module structure matches reference
        EXPECTED: FAIL - Current settings structure is flat, not environment-based
        """
        import os
        from pathlib import Path
        
        # Reference architecture has environment-based settings
        settings_dir = Path(settings.BASE_DIR) / 'vierla_project' / 'settings'
        
        expected_settings_files = [
            'base.py',
            'development.py', 
            'production.py',
            'testing.py',
            '__init__.py'
        ]
        
        # These files should exist but don't in current implementation
        for settings_file in expected_settings_files:
            file_path = settings_dir / settings_file
            self.assertTrue(
                file_path.exists(),
                f"Settings file {settings_file} should exist in environment-based structure"
            )


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
