"""
Vierla Backend - Development Settings
"""
from .base import *
import psycopg2

# Development specific settings
DEBUG = True

# Allow connections from network IP for mobile development
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', 'testserver', '************', '********']

def test_postgresql_connection():
    """Test if PostgreSQL is available and accessible."""
    try:
        conn = psycopg2.connect(
            host=os.environ.get("DB_HOST", "localhost"),
            port=os.environ.get("DB_PORT", "5432"),
            user=os.environ.get("DB_USER", "vierla_user"),
            password=os.environ.get("DB_PASSWORD", "vierla_password"),
            database=os.environ.get("DB_NAME", "vierla_db"),
            connect_timeout=5
        )
        conn.close()
        return True
    except (psycopg2.Error, Exception):
        return False

# Database configuration with intelligent fallback
USE_SQLITE = os.environ.get('USE_SQLITE', 'False').lower() == 'true'
POSTGRESQL_AVAILABLE = not USE_SQLITE and test_postgresql_connection()

if POSTGRESQL_AVAILABLE:
    # Primary PostgreSQL configuration with optimization settings
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": os.environ.get("DB_NAME", "vierla_db"),
            "USER": os.environ.get("DB_USER", "vierla_user"),
            "PASSWORD": os.environ.get("DB_PASSWORD", "vierla_password"),
            "HOST": os.environ.get("DB_HOST", "localhost"),
            "PORT": os.environ.get("DB_PORT", "5432"),
            # PostgreSQL optimization settings as per reference architecture
            "OPTIONS": {
                "sslmode": os.environ.get("DB_SSLMODE", "prefer"),
                "connect_timeout": 10,
                "options": "-c default_transaction_isolation=read_committed"
            },
            # Connection pooling and performance settings
            "CONN_MAX_AGE": 600,  # 10 minutes
            "CONN_HEALTH_CHECKS": True,
            "ATOMIC_REQUESTS": False,  # Disabled for better performance
        }
    }
    print("🐘 Using PostgreSQL database")
else:
    # Fallback to SQLite for development/testing if PostgreSQL not available
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }
    if USE_SQLITE:
        print("📁 Using SQLite database (configured)")
    else:
        print("📁 Using SQLite database (PostgreSQL fallback)")

# Development middleware
MIDDLEWARE += [
    # Add debug toolbar when available
]

# Debug toolbar configuration
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True

# Development-friendly throttle rates
REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {
    'anon': '1000/hour',  # Much higher for development
    'user': '10000/hour',
    'mobile': '2000/hour',
    'login': '100/minute',  # Much higher for testing
    'register': '50/minute',
    'password_reset': '30/hour',
}

# Disable HTTPS requirements in development
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = None

# Development logging
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['django']['level'] = 'DEBUG'
LOGGING['loggers']['apps']['level'] = 'DEBUG'

# Development cache (use local memory)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    },
    'sessions': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}
