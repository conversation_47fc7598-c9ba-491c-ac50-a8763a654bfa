/**
 * AUDIT TEST: Frontend Component Structure Violations
 * This test proves that the current component structure violates
 * atomic design principles and Rule R-005.
 */

import { describe, it, expect } from '@jest/globals';
import * as fs from 'fs';
import * as path from 'path';

describe('Component Structure Audit Tests', () => {
  const componentsDir = path.join(__dirname, '../../components');
  
  describe('AUDIT FINDING: Atomic Design Violation (Rule R-005)', () => {
    it('should have proper atomic design directory structure', () => {
      /**
       * AUDIT TEST: Verify atomic design structure exists
       * EXPECTED: FAIL - Current structure is flat, not atomic
       */
      const expectedAtomicDirs = ['atoms', 'molecules', 'organisms'];
      
      expectedAtomicDirs.forEach(dir => {
        const dirPath = path.join(componentsDir, dir);
        expect(fs.existsSync(dirPath)).toBe(true);
      });
    });
    
    it('should not have components in flat structure', () => {
      /**
       * AUDIT TEST: Verify components are not in flat structure
       * EXPECTED: FAIL - Components are currently in flat structure
       */
      if (fs.existsSync(componentsDir)) {
        const items = fs.readdirSync(componentsDir);
        const componentFiles = items.filter(item => 
          item.endsWith('.tsx') || item.endsWith('.ts')
        );
        
        // Should have no component files in root components directory
        expect(componentFiles.length).toBe(0);
      }
    });
  });
  
  describe('AUDIT FINDING: Hardcoded Colors Violation', () => {
    it('should not contain hardcoded color values in components', async () => {
      /**
       * AUDIT TEST: Verify no hardcoded colors exist
       * EXPECTED: FAIL - Hardcoded colors still exist in components
       */
      const hardcodedColorPatterns = [
        /#[0-9A-Fa-f]{6}/g, // Hex colors
        /#[0-9A-Fa-f]{3}/g,  // Short hex colors
        /rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/g, // RGB colors
        /rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)/g, // RGBA colors
      ];
      
      const violations: string[] = [];
      
      function scanDirectory(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
          const itemPath = path.join(dir, item);
          const stat = fs.statSync(itemPath);
          
          if (stat.isDirectory()) {
            scanDirectory(itemPath);
          } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
            const content = fs.readFileSync(itemPath, 'utf8');
            
            hardcodedColorPatterns.forEach(pattern => {
              const matches = content.match(pattern);
              if (matches) {
                violations.push(`${itemPath}: ${matches.join(', ')}`);
              }
            });
          }
        });
      }
      
      scanDirectory(componentsDir);
      
      // Should have no hardcoded colors
      expect(violations).toEqual([]);
    });
  });
  
  describe('AUDIT FINDING: Theme System Inconsistency', () => {
    it('should have single theme implementation', () => {
      /**
       * AUDIT TEST: Verify only one theme system exists
       * EXPECTED: FAIL - Multiple theme implementations exist
       */
      const themeDir = path.join(__dirname, '../../theme');
      const referenceThemeDir = path.join(__dirname, '../../../reference-code/frontend_v1/src/theme');
      
      // Should not have conflicting theme implementations
      if (fs.existsSync(themeDir) && fs.existsSync(referenceThemeDir)) {
        // This will fail as both exist
        expect(false).toBe(true);
      }
    });
    
    it('should use consistent color values across theme files', () => {
      /**
       * AUDIT TEST: Verify theme color consistency
       * EXPECTED: FAIL - Inconsistent color values exist
       */
      const themeFile = path.join(__dirname, '../../theme/index.ts');
      
      if (fs.existsSync(themeFile)) {
        const content = fs.readFileSync(themeFile, 'utf8');
        
        // Check for Vierla brand colors consistency
        const expectedColors = {
          primary: '#364035', // Forest Green
          secondary: '#8B9A8C', // Sage Green
          background: '#F4F1E8', // Warm Cream (not #FFFFFF)
        };
        
        Object.entries(expectedColors).forEach(([colorName, expectedValue]) => {
          const colorRegex = new RegExp(`${colorName}.*['"]([^'"]+)['"]`);
          const match = content.match(colorRegex);
          
          if (match) {
            expect(match[1]).toBe(expectedValue);
          }
        });
      }
    });
  });
  
  describe('AUDIT FINDING: Duplicate Component Violation (Rule R-003)', () => {
    it('should not have Enhanced prefixed components', () => {
      /**
       * AUDIT TEST: Verify no Enhanced components exist
       * EXPECTED: FAIL - Enhanced components still exist
       */
      const enhancedComponents: string[] = [];
      
      function findEnhancedComponents(dir: string) {
        if (!fs.existsSync(dir)) return;
        
        const items = fs.readdirSync(dir);
        
        items.forEach(item => {
          const itemPath = path.join(dir, item);
          const stat = fs.statSync(itemPath);
          
          if (stat.isDirectory()) {
            findEnhancedComponents(itemPath);
          } else if (item.startsWith('Enhanced') && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
            enhancedComponents.push(itemPath);
          }
        });
      }
      
      findEnhancedComponents(path.join(__dirname, '../../'));
      
      // Should have no Enhanced components
      expect(enhancedComponents).toEqual([]);
    });
  });
});
