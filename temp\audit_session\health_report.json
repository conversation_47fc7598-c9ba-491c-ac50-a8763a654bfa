{"audit_session_id": "audit-2025-08-08", "timestamp": "2025-08-08T00:10:00Z", "health_check_results": {"backend": {"test_suite_status": "CRITICAL_FAILURES", "total_tests": 171, "passed_tests": 141, "failed_tests": 30, "pass_rate": "82.5%", "critical_issues": [{"category": "Database Configuration", "severity": "CRITICAL", "description": "PostgreSQL misconfiguration - system falling back to SQLite", "affected_tests": ["test_postgresql_configuration_matches_reference", "test_database_settings_configuration", "test_postgresql_connection_available"]}, {"category": "ALLOWED_HOSTS Configuration", "severity": "HIGH", "description": "Missing required hosts in ALLOWED_HOSTS for mobile development", "affected_tests": ["test_localhost_allowed", "test_network_ip_192_168_2_65_allowed", "test_android_emulator_ip_allowed"]}, {"category": "Environment Detection", "severity": "MEDIUM", "description": "Environment detection incorrectly identifying production environment", "affected_tests": ["test_environment_detection_production"]}], "warnings": ["CheckConstraint.check is deprecated in favor of .condition", "Database access not allowed in some tests"]}, "frontend": {"test_suite_status": "MAJOR_FAILURES", "total_tests": 1370, "passed_tests": 998, "failed_tests": 372, "pass_rate": "72.8%", "critical_issues": [{"category": "Module Resolution", "severity": "CRITICAL", "description": "Jest configuration issues with ECMAScript modules", "affected_modules": ["expo-image-manipulator", "expo-image-picker"]}, {"category": "Component Testing", "severity": "HIGH", "description": "Profile screen components failing to render properly", "affected_components": ["ProfileScreen", "ProfileDisplay", "AvatarUpload"]}, {"category": "API Integration", "severity": "HIGH", "description": "Network error handling and API client configuration issues", "affected_tests": ["loginIntegration.test.ts"]}, {"category": "Theme Provider", "severity": "MEDIUM", "description": "Theme context not properly providing values to components", "affected_tests": ["ThemeProvider.test.tsx"]}], "duplicate_files_violation": {"rule": "R-003", "description": "Found files with 'Enhanced' pattern violating duplicate elimination rule", "files": ["test-enhanced-profile-features.js"]}}}, "system_health_summary": {"overall_status": "CRITICAL", "backend_health": "DEGRADED", "frontend_health": "DEGRADED", "database_connectivity": "FAILED", "test_infrastructure": "PARTIALLY_FUNCTIONAL", "immediate_action_required": true}}