"""
AUDIT TEST: Task List Data Integrity Violations
This test proves that the task_list.md file has data integrity issues
including duplicate epic_ids and inconsistent formatting.
"""

import pytest
import re
from pathlib import Path


class TaskListIntegrityAuditTest:
    """
    AUDIT FINDING: Task list data integrity violations
    EXPECTED RESULT: These tests should FAIL, proving the issues exist
    """
    
    def setup_method(self):
        """Setup test data"""
        self.task_list_path = Path('augment-docs/task_list.md')
        if self.task_list_path.exists():
            with open(self.task_list_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
        else:
            self.content = ""
    
    def test_no_duplicate_epic_ids(self):
        """
        AUDIT TEST: Verify no duplicate epic_ids exist
        EXPECTED: FAIL - Duplicate EPIC-AD-HOC-04 entries exist
        """
        # Extract all epic_ids from the content
        epic_id_pattern = r'epic_id:\s*([^\s\n]+)'
        epic_ids = re.findall(epic_id_pattern, self.content)
        
        # Check for duplicates
        seen_ids = set()
        duplicates = []
        
        for epic_id in epic_ids:
            if epic_id in seen_ids:
                duplicates.append(epic_id)
            seen_ids.add(epic_id)
        
        # Should have no duplicates but will fail due to EPIC-AD-HOC-04
        assert len(duplicates) == 0, f"Found duplicate epic_ids: {duplicates}"
    
    def test_epic_id_format_consistency(self):
        """
        AUDIT TEST: Verify epic_id format consistency
        EXPECTED: FAIL - Inconsistent epic_id formats exist
        """
        epic_id_pattern = r'epic_id:\s*([^\s\n]+)'
        epic_ids = re.findall(epic_id_pattern, self.content)
        
        # Valid formats: EPIC-XX, EPIC-AUDIT-XX, EPIC-PARITY-XX, EPIC-AD-HOC-XX
        valid_patterns = [
            r'^EPIC-\d{2}$',  # EPIC-01, EPIC-02, etc.
            r'^EPIC-AUDIT-\d{2}$',  # EPIC-AUDIT-01, etc.
            r'^EPIC-PARITY-\d{2}$',  # EPIC-PARITY-01, etc.
            r'^EPIC-AD-HOC-\d{2}$',  # EPIC-AD-HOC-01, etc.
        ]
        
        invalid_ids = []
        for epic_id in epic_ids:
            is_valid = any(re.match(pattern, epic_id) for pattern in valid_patterns)
            if not is_valid:
                invalid_ids.append(epic_id)
        
        # Should have no invalid IDs
        assert len(invalid_ids) == 0, f"Found invalid epic_id formats: {invalid_ids}"
    
    def test_status_consistency(self):
        """
        AUDIT TEST: Verify status field consistency
        EXPECTED: FAIL - Inconsistent status values exist
        """
        # Extract all status values
        status_pattern = r'status:\s*([^\n]+)'
        statuses = re.findall(status_pattern, self.content)
        
        # Valid statuses
        valid_statuses = ['Pending', 'In Progress', 'Complete', 'Completed', 'Cancelled']
        
        invalid_statuses = []
        for status in statuses:
            status = status.strip()
            if status not in valid_statuses:
                invalid_statuses.append(status)
        
        # Should have no invalid statuses
        assert len(invalid_statuses) == 0, f"Found invalid statuses: {invalid_statuses}"
    
    def test_priority_field_consistency(self):
        """
        AUDIT TEST: Verify priority field consistency
        EXPECTED: FAIL - Missing or inconsistent priority values exist
        """
        # Extract all epic blocks
        epic_blocks = re.findall(r'### EPIC-[^#]+?(?=### EPIC-|\Z)', self.content, re.DOTALL)
        
        missing_priority = []
        invalid_priorities = []
        valid_priorities = ['Highest', 'High', 'Medium', 'Low']
        
        for i, block in enumerate(epic_blocks):
            epic_id_match = re.search(r'epic_id:\s*([^\s\n]+)', block)
            epic_id = epic_id_match.group(1) if epic_id_match else f"Block_{i}"
            
            priority_match = re.search(r'priority:\s*([^\n]+)', block)
            if not priority_match:
                missing_priority.append(epic_id)
            else:
                priority = priority_match.group(1).strip()
                if priority not in valid_priorities:
                    invalid_priorities.append(f"{epic_id}: {priority}")
        
        # Should have no missing or invalid priorities
        assert len(missing_priority) == 0, f"EPICs missing priority: {missing_priority}"
        assert len(invalid_priorities) == 0, f"EPICs with invalid priority: {invalid_priorities}"
    
    def test_epic_structure_completeness(self):
        """
        AUDIT TEST: Verify all EPICs have required fields
        EXPECTED: FAIL - Some EPICs missing required fields
        """
        epic_blocks = re.findall(r'### EPIC-[^#]+?(?=### EPIC-|\Z)', self.content, re.DOTALL)
        
        required_fields = ['epic_id', 'status', 'priority', 'description']
        incomplete_epics = []
        
        for i, block in enumerate(epic_blocks):
            epic_id_match = re.search(r'epic_id:\s*([^\s\n]+)', block)
            epic_id = epic_id_match.group(1) if epic_id_match else f"Block_{i}"
            
            missing_fields = []
            for field in required_fields:
                if not re.search(rf'{field}:\s*[^\n]+', block):
                    missing_fields.append(field)
            
            if missing_fields:
                incomplete_epics.append(f"{epic_id}: missing {missing_fields}")
        
        # Should have no incomplete EPICs
        assert len(incomplete_epics) == 0, f"Incomplete EPICs: {incomplete_epics}"


def test_run_audit():
    """Run the audit test suite"""
    audit = TaskListIntegrityAuditTest()
    audit.setup_method()
    
    # These tests should fail, proving the issues exist
    try:
        audit.test_no_duplicate_epic_ids()
        print("❌ AUDIT FAILED: No duplicate epic_ids found (expected to find duplicates)")
    except AssertionError as e:
        print(f"✅ AUDIT VERIFIED: {e}")
    
    try:
        audit.test_epic_id_format_consistency()
        print("❌ AUDIT FAILED: All epic_ids have consistent format (expected inconsistencies)")
    except AssertionError as e:
        print(f"✅ AUDIT VERIFIED: {e}")
    
    try:
        audit.test_status_consistency()
        print("❌ AUDIT FAILED: All statuses are consistent (expected inconsistencies)")
    except AssertionError as e:
        print(f"✅ AUDIT VERIFIED: {e}")


if __name__ == '__main__':
    test_run_audit()
